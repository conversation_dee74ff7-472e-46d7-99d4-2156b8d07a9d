{ // Use IntelliSense to learn about possible attributes.                                                            // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Comfy-Debug-1",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "program": "main.py --multi-user --use-pytorch-cross-attention --listen 0.0.0.0 --port 18895",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}:/home/<USER>/.conda/envs/qsbase/lib/python3.12/site-packages:$PYTHONPATH"
            },
            "args": ""
        },
        {
            "name": "Comfy-Debug-2",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "program": "main.py --multi-user --use-pytorch-cross-attention --listen 0.0.0.0 --port 28891",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}:/home/<USER>/.conda/envs/qsbase/lib/python3.12/site-packages:$PYTHONPATH"
            },
            "args": ""
        },
        {
            "name": "Comfy-Debug-No custom loading",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "program": "main.py --disable-all-custom-nodes --multi-user --use-pytorch-cross-attention --listen 0.0.0.0 --port 18894",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": "",
            "env": {
                "PYTHONPATH": "${workspaceFolder}:/home/<USER>/.conda/envs/qsbase/lib/python3.12/site-packages:$PYTHONPATH"
            }
        }
    ]
}