#!/bin/bash

# Default values
base_dir=$(dirname "$0")
DEFAULT_SOURCE_DIR="/qsfs2/services/qsComfy/custom_nodes"
DEFAULT_TARGET_DIR="$base_dir/custom_nodes"

# Read arguments
SOURCE_DIR="${1:-$DEFAULT_SOURCE_DIR}"
TARGET_DIR="${2:-$DEFAULT_TARGET_DIR}"

# Iterate over subdirectories of SOURCE_DIR
for sub in "$SOURCE_DIR"/*; do
    [ -d "$sub" ] || continue  # skip non-directories
    
    
    dir_name=$(basename "$sub")
    source_dir="$SOURCE_DIR/$dir_name"
    target_dir="$TARGET_DIR/$dir_name"

    echo "Mounting $source_dir to $target_dir"
    sudo fusermount -u "$target_dir" 2> /dev/null
    # Skip if subdirectory is a mount point
    if mountpoint -q "$sub"; then
        echo "Skipping $sub (it is a mount point)"
        continue
    fi

    mkdir -p "$target_dir"
    sudo bindfs --perms=a+w "$source_dir" "$target_dir"
done
