version: '3'
services:
  qscomfy:
    image: qsacr001.azurecr.io/qs-comfy:prod_1.0.0
    container_name: prod-qscomfy
    environment:
      - QSCOMFY_ARGS=--use-pytorch-cross-attention --enable-cors-header --multi-user --listen 0.0.0.0
    volumes:
       - ./custom_nodes:/qsComfy/custom_nodes
       - ./models:/qsComfy/models
       - ./input:/qsComfy/input
       - ./output:/qsComfy/output
       - ./artlet:/qsComfy/artlet
       - ./user:/qsComfy/user
       - ./my_workflows:/qsComfy/my_workflows
   
    logging:
      driver: "json-file"
      options:
        labels: "com.example.logging"
        env: "os,customer"
      
    ports: 
      - 28895:28890
      - 28896:28891
      - 28897:28892
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
              
