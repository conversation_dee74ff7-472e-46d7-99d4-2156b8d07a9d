#!/bin/bash
# Default values
base_dir=$(dirname "$0")
DEFAULT_SOURCE_DIR=$base_dir/qs_custom_nodes
DEFAULT_TARGET_DIR="./custom_nodes"

# Read arguments
SOURCE_DIR="${1:-$DEFAULT_SOURCE_DIR}"
TARGET_DIR="${2:-$DEFAULT_TARGET_DIR}"
for dir in "$SOURCE_DIR"/*; do
    if [ -d "$dir" ]; then  # Check if it is a directory
        node_name=$(basename "$dir")
        target_node_dir="$TARGET_DIR/$node_name"
        #check if the target directory is  mounted already if so unmount it
        sudo fusermount -u "$target_node_dir" 2> /dev/null
        
        echo "Mounting $dir to $target_node_dir..."
        mkdir -p "$target_node_dir"
        sudo bindfs --perms=a+w "$dir" "$target_node_dir"
    else
        echo "Skipping non-directory: $dir"
    fi
done
