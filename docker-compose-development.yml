version: '3'
services:
  qscomfy:
    image: qsacr001.azurecr.io/qs-comfy:latest
    environment:
      - QSCOMFY_ARGS=--use-pytorch-cross-attention --enable-cors-header --multi-user --listen 0.0.0.0
    volumes:
      - ./:/qsComfy
   
    logging:
      driver: "json-file"
      options:
        labels: "com.example.logging"
        env: "os,customer"
      
    network_mode: "host"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
              
