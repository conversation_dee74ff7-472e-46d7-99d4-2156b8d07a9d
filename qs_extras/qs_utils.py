import json
from nodes import latent_preview, common_ksampler
import comfy.samplers
import comfy.sample
import torch
import math
import io
from PIL import Image
import numpy as np



from torch.nn import functional as F
from collections import deque
import folder_paths

# print("Harsh-Node Reading")

class AnyType(str):
  """A special class that is always equal in not equal comparisons. Credit to pythongosssss"""

  def __ne__(self, __value: object) -> bool:
    return False
any = AnyType("*")

class ByPassTypeTuple(tuple):
	def __getitem__(self, index):
		if index > 0:
			index = 0
		item = super().__getitem__(index)
		if isinstance(item, str):
			return AnyType(item)
		return item


class QSPointEditorFix:
    def __init__(self):
        pass
    @classmethod
    def INPUT_TYPES(cls):
        
        return {
            "required": {
                "original_width": ("INT", { "forceInput": True}),
                "original_height": ("INT", { "forceInput": True}),
                "reference_width": ("INT", { "forceInput": True}),
                "reference_height": ("INT", { "forceInput": True}),
            },
            "optional" : {
                "positive_coords": ("STRING", { "forceInput": True}),
                "negative_coords": ("STRING", { "forceInput": True}),
                "bbox": ("BBOX", { "forceInput": True}),
            }
        }

    RETURN_TYPES = ("STRING", "STRING", "BBOX")
    RETURN_NAMES = ("positive_coords", "negative_coords", "bbox")
    FUNCTION = "modify"
    CATEGORY = "QS/segmentation"


    def modify(self, original_width, original_height, reference_width, reference_height, positive_coords=None, negative_coords=None, bbox=None):
        if positive_coords:
            pc=json.loads(positive_coords)
            for point in pc:
                point['x']=int(point['x'] * ( original_width / reference_width))
                point['y']=int(point['y'] * ( original_height / reference_height))
            positive_coords=json.dumps(pc)
        else :
            positive_coords="[{}]"
        
        if negative_coords:
            nc = json.loads(negative_coords)
            for point in nc:
                point['x'] = int(point['x'] * (original_width / reference_width))
                point['y'] = int(point['y'] * (original_height / reference_height))
            negative_coords = json.dumps(nc)
        else :
            negative_coords="[{}]"
        
        if bbox:
            if bbox[0]:
                x1, y1, x2, y2 = bbox[0][0], bbox[0][1], bbox[0][2], bbox[0][3]
                scaled_x1 = int(x1 * (original_width / reference_width))
                scaled_y1 = int(y1 * (original_height / reference_height))
                scaled_x2 = int(x2 * (original_width / reference_width))
                scaled_y2 = int(y2 * (original_height / reference_height))
                bbox = [(scaled_x1, scaled_y1, scaled_x2, scaled_y2)]
            else :
                bbox=[(0,0,original_width,original_height)]
        else :
            bbox=[(0,0,original_width,original_height)]
        
        return (positive_coords, negative_coords, bbox)

class QSAutoMaskPadding:
    def __init__(self):
        pass
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "optional" : {
                "image": ("IMAGE",),
                "mask": ("MASK",),
                "actual": ("BOOLEAN", { "default": False }),
                "actual_width": ("INT", {"default": 1024, "min": 8, "step": 8}),
                "actual_height": ("INT", {"default": 1024, "min": 8, "step": 8}),
                "reference": ("BOOLEAN", { "default": True }),
                "reference_width": ("INT", {"default": 1024, "min": 8, "step": 8}),
                "reference_height": ("INT", {"default": 1024, "min": 8, "step": 8}),
                "padding": ("INT", {"default": 0, "min": 0, "step": 1}),
                "extend_image": ("BOOLEAN", { "default": False }),
                "multiple_of": ("INT", {"default": 1, "min": 1, "step": 1}),
            }
        }

    RETURN_TYPES = ("IMAGE", "MASK", "INT", "INT", "INT", "INT")
    RETURN_NAMES = ("Image", "Mask", "x", "y", "width", "height")
    FUNCTION = "modify"
    CATEGORY = "QS/mask"


    def modify(self, image=None, mask=None, actual=False, actual_width=1024, actual_height=1024, reference=True, reference_width=1024, reference_height=1024, padding=0, extend_image=False, multiple_of=1):
        left=padding
        right=padding
        top=padding
        bottom=padding

        if mask is None and image is None:
            raise ValueError("Either image or mask must be provided")
            
        if mask is not None:
            _, y, x = torch.where(mask)

            x_min= x.min().item()
            x_max= x.max().item()
            y_min= y.min().item()
            y_max= y.max().item()

            if actual:
                reference=False
                if actual_height>y_max-y_min:
                    top+=int(math.floor((actual_height-y_max+y_min)/2))
                    bottom+=int(math.ceil((actual_height-y_max+y_min)/2))
                if actual_width>x_max-x_min:
                    left+=int(math.floor((actual_width-x_max+x_min)/2))
                    right+=int(math.ceil((actual_width-x_max+x_min)/2))
                    
            bottom+=(multiple_of-(y_max-y_min+top+bottom)%multiple_of)%multiple_of
            right+=(multiple_of-(x_max-x_min+left+right)%multiple_of)%multiple_of

            if reference:
                if (x_max-x_min)/(y_max-y_min)>reference_width/reference_height:
                    p_w=int((x_max-x_min+left+right)*reference_height/reference_width- (y_max-y_min+top+bottom))
                    top+=int(math.floor(p_w/2))
                    bottom+=int(math.ceil(p_w/2))
                elif (x_max-x_min)/(y_max-y_min)<reference_width/reference_height:
                    p_h=int((y_max-y_min+top+bottom)*reference_width/reference_height- (x_max-x_min+left+right))
                    left+=int(math.floor(p_h/2))
                    right+=int(math.ceil(p_h/2))

            if y_min<top:
                out_y=top-y_min
                top=y_min
                bottom+=out_y
                if y_max+bottom>mask.shape[1]:
                    top+=int((y_max+bottom-mask.shape[1])/2)
                    bottom-=int((y_max+bottom-mask.shape[1])/2)
            elif y_max+bottom>mask.shape[1]:
                out_y=y_max+bottom-mask.shape[1]
                top+=out_y
                bottom=mask.shape[1]-y_max
                if y_min<top:
                    bottom+=int((top-y_min)/2)
                    top-=int((top-y_min)/2)
            if x_min<left:
                out_x=left-x_min
                left=x_min
                right+=out_x
                if x_max+right>mask.shape[2]:
                    left+=int((x_max+right-mask.shape[2])/2)
                    right-=int((x_max+right-mask.shape[2])/2)
            elif x_max+right>mask.shape[2]:
                out_x=x_max+right-mask.shape[2]
                left+=out_x
                right=mask.shape[2]-x_max
                if x_min<left:
                    right+=int((left-x_min)/2)
                    left-=int((left-x_min)/2)

            if not extend_image:
                if top>y_min:
                    c1=top-y_min
                    top=y_min
                    left-=c1
                if bottom>mask.shape[1]-y_max:
                    c2=bottom-mask.shape[1]+y_max
                    bottom=mask.shape[1]-y_max
                    right-=c2
                if left>x_min:
                    c3=left-x_min
                    left=x_min
                    top-=c3
                if right>mask.shape[2]-x_max:
                    c4=right-mask.shape[2]+x_max
                    right=mask.shape[2]-x_max
                    bottom-=c4

            new_mask= torch.zeros((mask.shape[0], y_max-y_min+top+bottom, x_max-x_min+left+right), dtype=torch.float32)
            new_mask[:, top:y_max-y_min+top, left:x_max-x_min+left] = mask[:, y_min:y_max, x_min:x_max]
        else:
            new_mask = torch.zeros((image.shape[0], image.shape[1] + left+right, image.shape[2] + top+bottom), dtype=torch.float32,)


        if image is not None:
            B, h, w, _ = image.shape
            new_image = torch.zeros((B, new_mask.shape[1], new_mask.shape[2], 3), dtype=torch.float32,)
            new_image[:, max(0,top-y_min): min(new_mask.shape[1], h+top-y_min), max(0,left-x_min): min(new_mask.shape[2], w+left-x_min), :] = image[:, max(0,y_min-top): min(h, y_max+bottom), max(0,x_min-left): min(w, x_max+right), :3]

        return (new_image, new_mask, x_min-left, y_min-top, new_mask.shape[2], new_mask.shape[1])


class BlendThroughMaskOutside:
    def __init__(self):
        pass
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "mask": ("MASK",),  # Binary mask input
                "shift_pixels": ("INT", {"default": 5, "min": 1, "max": 100, "step": 1}),
                "boundary_value": ("FLOAT", {"default": 1, "min": 0.00, "max": 1.00, "step": 0.01}),
                "mid_value": ("FLOAT", {"default": 0.01, "min": 0.00, "max": 1.00, "step": 0.01}),
            }
        }

    RETURN_TYPES = ("MASK",)
    RETURN_NAMES = ("Mask",)
    FUNCTION = "apply_gradient"
    CATEGORY = "QS/mask"

    def apply_gradient(self, mask, shift_pixels, boundary_value, mid_value):
        mask_tensor = mask.clone().squeeze().to('cpu')  # Ensure it's on GPU
        h, w = mask_tensor.shape

        # Define a 3x3 kernel to find boundary pixels
        kernel = torch.tensor([[1, 1, 1],
                               [1, 0, 1],
                               [1, 1, 1]], dtype=torch.float32, device='cpu').view(1, 1, 3, 3)

        # Apply convolution to detect boundaries
        mask_exp = mask_tensor.unsqueeze(0).unsqueeze(0).float()  # Shape: (1, 1, H, W)
        neighbors = F.conv2d(mask_exp, kernel, padding=1).squeeze()
        boundary = (mask_tensor > 0) & (neighbors < 8)  # Detect edge pixels

        # BFS to propagate values
        queue = deque(torch.nonzero(boundary, as_tuple=False).tolist())
        flag = torch.zeros_like(mask_tensor, dtype=torch.bool)
        flag[boundary] = True

        for c in range(shift_pixels, 0, -1):
            if not queue:
                break

            value = (shift_pixels * mid_value - (mid_value - boundary_value) * c) / shift_pixels
            next_queue = deque()

            while queue:
                i, j = queue.popleft()
                mask_tensor[i, j] = value

                # Check 8 neighbors
                for di, dj in [(-1, 0), (-1, -1), (-1, 1), (0, -1), (0, 1), (1, 0), (1, -1), (1, 1)]:
                    ni, nj = i + di, j + dj
                    if 0 <= ni < h and 0 <= nj < w and mask_tensor[ni, nj] == 0 and not flag[ni, nj]:
                        flag[ni, nj] = True
                        next_queue.append((ni, nj))

            queue = next_queue  # Move to the next expansion layer

        return (mask_tensor.unsqueeze(0),)


class QSHalfPreviewKSampler:
    def __init__(self):
        pass
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "model": ("MODEL",),
                "seed": ("INT", {"default": 0, "min": 0, "max": 0xffffffffffffffff}),
                "steps": ("INT", {"default": 20, "min": 1, "max": 10000}),
                "cfg": ("FLOAT", {"default": 1.0}),
                "positive": ("CONDITIONING", ),
                "negative": ("CONDITIONING", ),
                "latent_image": ("LATENT", ),
                "sampler_name": ("STRING", {"default": "euler"}),
                "scheduler": ("STRING", {"default": "normal"}),
                "denoise": ("FLOAT", {"default": 1.0}),
            }
        }

    RETURN_TYPES = ("LATENT", "IMAGE")
    RETURN_NAMES = ("latent", "preview_frames")
    FUNCTION = "sample"
    CATEGORY = "QS/sampling"

    def sample(self, model, seed, steps, positive, negative, latent_image, sampler_name="euler", scheduler="normal", denoise=1.0, cfg=1.0):
        # Store the original prepare_callback function
        original_prepare_callback = latent_preview.prepare_callback
        
        # Create a list to store preview frames
        preview_frames = []
        
        # Get the previewer
        previewer = latent_preview.get_previewer(model.load_device, model.model.latent_format)
        
        # Override the prepare_callback function temporarily
        def custom_prepare_callback(model, steps):
            original_cb = original_prepare_callback(model, steps)
            if original_cb is None:
                return None
            
            def half_preview_callback(step, x0, x, total_steps):
                half_width = x0.shape[3] // 2
                preview_x0 = x0.clone()
                preview_x0 = preview_x0[:, :, :, half_width:]
                
                # Generate preview image and store it
                if previewer:
                    # Decode the preview image
                    preview_data = previewer.decode_latent_to_preview_image("JPEG", preview_x0)
                    if preview_data and isinstance(preview_data, tuple) and len(preview_data) > 1:
                        preview_image = preview_data[1]  # Get the image data from the tuple
                        if preview_image:
                            # Convert bytes to image tensor
                            image = np.array(preview_image)
                            # Convert PIL image to tensor
                            preview_tensor = torch.from_numpy(np.array(image).astype(np.float32) / 255.0)
                            preview_frames.append(preview_tensor)
                
                # Call original callback for live preview
                original_cb(step, preview_x0, x, total_steps)
            
            return half_preview_callback
        
        # Replace the prepare_callback function
        latent_preview.prepare_callback = custom_prepare_callback
        
        try:
            # Call common_ksampler
            result = common_ksampler(model, seed, steps, cfg, sampler_name, scheduler, 
                                   positive, negative, latent_image, denoise=denoise)
        finally:
            # Restore the original prepare_callback function
            latent_preview.prepare_callback = original_prepare_callback
        
        # Stack all preview frames into a single tensor
        if preview_frames:
            preview_frames_tensor = torch.stack(preview_frames)
        else:
            # Return empty tensor if no previews were generated
            preview_frames_tensor = torch.zeros((1, 64, 64, 3))
        
        return (result[0], preview_frames_tensor)

class LoraSelect:
    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "lora_name": (folder_paths.get_filename_list("loras"), {"tooltip": "The name of the LoRA."}),
            }
        }

    RETURN_TYPES = (any,)
    RETURN_NAMES = ("lora_name",)
    FUNCTION = "load_select"
    CATEGORY = "QS/sampling"

    def load_select(self, lora_name):
        return (lora_name,)

class QSPack:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "inputcount": ("INT", {"default": 1, "min": 1, "max": 1000, "step": 1}),
                "input_1": (any,),
            }
        }

    RETURN_TYPES = (any,)
    RETURN_NAMES = ("packed",)
    FUNCTION = "combine"
    CATEGORY = "QS/utils"

    def combine(self, inputcount, **kwargs):
        pack = []
        for c in range(1, inputcount + 1):
            input_key = f"input_{c}"
            
            if input_key in kwargs:
                input = kwargs[input_key]
                if isinstance(input, dict) and len(input) == 1:
                    key, value = next(iter(input.items()))
                    if isinstance(key, str):
                        pack.append((key,value))
                    else:
                        pack.append((f"input_{c}",input))
                else:
                    pack.append((f"input_{c}",input))
                
        return (pack,)  # Return a tuple containing the dictionary

class QSUnpack:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "packed": (any,),
                "outputcount": ("INT", {"default": 1, "min": 1, "max": 1000, "step": 1}),
            }
        }

    FUNCTION = "unpack"
    CATEGORY = "QS/utils"

    RETURN_TYPES = ByPassTypeTuple((any,))
    RETURN_NAMES = ByPassTypeTuple(("input_1",))

    def unpack(self, packed, outputcount):
        output_names=[]
        output_values=[]

        if not isinstance(packed, list):
            raise ValueError("Input must be a packed list from QSPack node")
        for c in range(0, outputcount):
            if c < len(packed):
                output_names.append(packed[c][0])
                output_values.append(packed[c][1])
            else:
                output_names.append(f"input_{c+1}")
                output_values.append("")
            
        return {"ui": {
            "text": output_names}, 
            "result": (output_values) 
        }

class QSNaming:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "input": (any,),
                "name": ("STRING",{"default":"input_1"}),
            }
        }

    FUNCTION = "naming"
    CATEGORY = "QS/utils"

    RETURN_TYPES = (any,)
    RETURN_NAMES = ("output",)

    def naming(self, input, name):
        pack = {}
        pack[name] = input
                
        return (pack,)  # Return a tuple containing the dictionary

class QSPreviewBridge:
    @classmethod
    def INPUT_TYPES(s):
        return {"required": {
                    "images": ("IMAGE",),
                    "image": ("STRING", {"default": ""}),
                    },
                "optional": {
                    "block": ("BOOLEAN", {"default": False, "label_on": "if_empty_mask", "label_off": "never", "tooltip": "is_empty_mask: If the mask is empty, the execution is stopped.\nnever: The execution is never stopped."}),
                    "restore_mask": (["never", "always", "if_same_size"], {"tooltip": "if_same_size: If the changed input image is the same size as the previous image, restore using the last saved mask\nalways: Whenever the input image changes, always restore using the last saved mask\nnever: Do not restore the mask.\n`restore_mask` has higher priority than `block`"}),
                    },
                "hidden": {"unique_id": "UNIQUE_ID", "extra_pnginfo": "EXTRA_PNGINFO"},
                }

    RETURN_TYPES = ("IMAGE", "MASK", )

    FUNCTION = "doit"

    OUTPUT_NODE = True

    CATEGORY = "ImpactPack/Util"

    DESCRIPTION = "This is a feature that allows you to edit and send a Mask over a image.\nIf the block is set to 'is_empty_mask', the execution is stopped when the mask is empty."

    def __init__(self):
        super().__init__()
        self.output_dir = folder_paths.get_temp_directory()
        self.type = "temp"
        self.prev_hash = None

    @staticmethod
    def load_image(pb_id):
        is_fail = False
        if pb_id not in core.preview_bridge_image_id_map:
            is_fail = True

        image_path, ui_item = core.preview_bridge_image_id_map[pb_id]

        if not os.path.isfile(image_path):
            is_fail = True

        if not is_fail:
            i = Image.open(image_path)
            i = ImageOps.exif_transpose(i)
            image = i.convert("RGB")
            image = np.array(image).astype(np.float32) / 255.0
            image = torch.from_numpy(image)[None,]

            if 'A' in i.getbands():
                mask = np.array(i.getchannel('A')).astype(np.float32) / 255.0
                mask = 1. - torch.from_numpy(mask)
            else:
                mask = torch.zeros((64, 64), dtype=torch.float32, device="cpu")
        else:
            image = empty_pil_tensor()
            mask = torch.zeros((64, 64), dtype=torch.float32, device="cpu")
            ui_item = {
                "filename": 'empty.png',
                "subfolder": '',
                "type": 'temp'
            }

        return image, mask.unsqueeze(0), ui_item

    def doit(self, images, image, unique_id, block=False, restore_mask="never", prompt=None, extra_pnginfo=None):
        need_refresh = False

        if unique_id not in core.preview_bridge_cache:
            need_refresh = True

        elif core.preview_bridge_cache[unique_id][0] is not images:
            need_refresh = True

        if not need_refresh:
            pixels, mask, path_item = PreviewBridge.load_image(image)
            image = [path_item]
        else:
            if restore_mask != "never":
                mask = core.preview_bridge_last_mask_cache.get(unique_id)
                if mask is None or (restore_mask != "always" and mask.shape[1:] != images.shape[1:3]):
                    mask = None
            else:
                mask = None

            if mask is None:
                mask = torch.zeros((64, 64), dtype=torch.float32, device="cpu")
                res = nodes.PreviewImage().save_images(images, filename_prefix="PreviewBridge/PB-", prompt=prompt, extra_pnginfo=extra_pnginfo)
            else:
                masked_images = tensor_convert_rgba(images)
                resized_mask = resize_mask(mask, (images.shape[1], images.shape[2])).unsqueeze(3)
                resized_mask = 1 - resized_mask
                tensor_putalpha(masked_images, resized_mask)
                res = nodes.PreviewImage().save_images(masked_images, filename_prefix="PreviewBridge/PB-", prompt=prompt, extra_pnginfo=extra_pnginfo)

            image2 = res['ui']['images']
            pixels = images

            path = os.path.join(folder_paths.get_temp_directory(), 'PreviewBridge', image2[0]['filename'])
            core.set_previewbridge_image(unique_id, path, image2[0])
            core.preview_bridge_image_id_map[image] = (path, image2[0])
            core.preview_bridge_image_name_map[unique_id, path] = (image, image2[0])
            core.preview_bridge_cache[unique_id] = (images, image2)

            image = image2

        is_empty_mask = torch.all(mask == 0)

        if block and is_empty_mask and core.is_execution_model_version_supported():
            from comfy_execution.graph import ExecutionBlocker
            result = ExecutionBlocker(None), ExecutionBlocker(None)
        elif block and is_empty_mask:
            print(f"[Impact Pack] PreviewBridge: ComfyUI is outdated - blocking feature is disabled.")
            result = pixels, mask
        else:
            result = pixels, mask

        if not is_empty_mask:
            core.preview_bridge_last_mask_cache[unique_id] = mask

        return {
            "ui": {"images": image},
            "result": result,
        }



NODE_CLASS_MAPPINGS = {
    "QSPointEditorFix": QSPointEditorFix,
    "QSAutoMaskPadding": QSAutoMaskPadding,
    "BlendThroughMaskOutside": BlendThroughMaskOutside,
    "QSHalfPreviewKSampler": QSHalfPreviewKSampler,
    "LoraSelect": LoraSelect,
    "QSPack": QSPack,
    "QSUnpack": QSUnpack,
    "QSNaming": QSNaming,
    "QSPreviewBridge": QSPreviewBridge,
}
NODE_DISPLAY_NAME_MAPPINGS = {
    "QSPointEditorFix": "QS Point Editor Fix",
    "QSAutoMaskPadding": "QS Auto Mask Padding",
    "BlendThroughMaskOutside": "QS Blend Through Mask Outside",
    "QSHalfPreviewKSampler": "QS Sampler",
    "LoraSelect": "QS Lora Select",
    "QSPack": "QS Pack",
    "QSUnpack": "QS Unpack",
    "QSNaming": "QS Naming",
    "QSPreviewBridge": "QS Preview Bridge",
}
WEB_DIRECTORY = "./web"
EXTENSION_WEB_DIRS = {"qs_utils": WEB_DIRECTORY}

API_EXPORTS = {
    "ANY_TYPE": any  # This will expose your 'any' value
}
