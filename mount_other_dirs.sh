#!/bin/bash
# Default values
base_dir=$(dirname "$0")
DEFAULT_SOURCE_DIR="/qsfs2/services/qsComfy/"
DEFAULT_TARGET_DIR="$base_dir"

# Read arguments
SOURCE_DIR="${1:-$DEFAULT_SOURCE_DIR}"
TARGET_DIR="${2:-$DEFAULT_TARGET_DIR}"

#iterate over input outpu as dtypes
for dtyps in input output models my_workflows user artlet custom_nodes
do
    source_dir="$SOURCE_DIR/$dtyps/"
    target_dir="$TARGET_DIR/$dtyps/"
    echo "mounting $source_dir to $target_dir"
    sudo fusermount -u "$target_dir" 2> /dev/null
    # Mount using bindfs
    mkdir -p "$target_dir"
    sudo bindfs --perms=a+w "$source_dir" "$target_dir"
   
done


echo "Operation completed!"
